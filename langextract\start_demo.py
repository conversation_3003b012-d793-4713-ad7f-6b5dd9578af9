#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangExtract 启动演示脚本
检查环境并提供使用指导
"""

import os
import sys
from pathlib import Path

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查虚拟环境
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 虚拟环境已激活")
    else:
        print("⚠️  建议在虚拟环境中运行")
    
    # 检查LangExtract安装
    try:
        import langextract as lx
        print("✅ LangExtract 已安装")
    except ImportError:
        print("❌ LangExtract 未安装")
        return False
    
    # 检查.env文件
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env 配置文件存在")
        
        # 检查API密钥配置
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'your-gemini-api-key-here' in content:
                print("⚠️  请在 .env 文件中设置实际的 API 密钥")
            else:
                print("✅ API 密钥已配置")
    else:
        print("⚠️  .env 配置文件不存在")
    
    return True

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "="*60)
    print("🚀 LangExtract 使用指南")
    print("="*60)
    
    print("\n📋 快速开始步骤:")
    print("1. 获取 Gemini API 密钥:")
    print("   - 访问: https://aistudio.google.com/")
    print("   - 创建 API 密钥")
    
    print("\n2. 配置 API 密钥:")
    print("   - 编辑 .env 文件")
    print("   - 将 'your-gemini-api-key-here' 替换为实际密钥")
    
    print("\n3. 运行演示:")
    print("   - 简单版本: python quickstart_gemini.py")
    print("   - 完整版本: python demo_gemini.py")
    
    print("\n📁 文件说明:")
    print("   - quickstart_gemini.py  : 最简单的使用示例")
    print("   - demo_gemini.py        : 完整功能演示（含可视化）")
    print("   - .env                  : 环境变量配置")
    print("   - 部署说明.md           : 详细部署文档")
    
    print("\n🔧 支持的模型:")
    print("   - gemini-2.5-flash     : 推荐，速度快成本低")
    print("   - gemini-2.5-pro       : 复杂任务，质量高")
    
    print("\n💡 示例用途:")
    print("   - 从文本中提取人物、地点、时间")
    print("   - 医疗报告结构化")
    print("   - 法律文档信息提取")
    print("   - 新闻文章要素提取")

def main():
    """主函数"""
    print("🎯 LangExtract 本地部署检查")
    print("-" * 40)
    
    if not check_environment():
        print("\n❌ 环境检查失败，请先安装 LangExtract")
        return
    
    show_usage_guide()
    
    print("\n" + "="*60)
    print("✨ 部署完成！现在可以开始使用 LangExtract 了")
    print("="*60)
    
    # 提供交互式选择
    print("\n🎮 选择操作:")
    print("1. 查看快速开始脚本")
    print("2. 查看完整演示脚本") 
    print("3. 查看部署说明文档")
    print("4. 退出")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            print("\n📄 快速开始脚本内容:")
            print("文件: quickstart_gemini.py")
            print("运行: python quickstart_gemini.py")
            
        elif choice == "2":
            print("\n📄 完整演示脚本内容:")
            print("文件: demo_gemini.py")
            print("运行: python demo_gemini.py")
            print("功能: 包含可视化HTML生成")
            
        elif choice == "3":
            print("\n📖 部署说明文档:")
            print("文件: 部署说明.md")
            print("包含: 详细的部署步骤和使用说明")
            
        elif choice == "4":
            print("👋 再见！")
            
        else:
            print("⚠️  无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 再见！")

if __name__ == "__main__":
    main()

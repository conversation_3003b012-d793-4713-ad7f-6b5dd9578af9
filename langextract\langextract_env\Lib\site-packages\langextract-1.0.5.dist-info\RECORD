__editable__.langextract-1.0.5.pth,sha256=E9VoOcR92zDe6ILNuEdNN55xm_Lw0I7crLZcD9XQI1s,93
__editable___langextract_1_0_5_finder.py,sha256=1pY2CnHLVXoGDdXJNsEJfD9XXRN6O1so418Tqa81hT0,3392
__pycache__/__editable___langextract_1_0_5_finder.cpython-313.pyc,,
langextract-1.0.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
langextract-1.0.5.dist-info/METADATA,sha256=ReRmW2ZHq6SzofG4wae27Ryidv4kAtvd_CBOz8UzjYs,18494
langextract-1.0.5.dist-info/RECORD,,
langextract-1.0.5.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
langextract-1.0.5.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
langextract-1.0.5.dist-info/direct_url.json,sha256=qSoh6m8lh95wz8Nap9B_keB_994ytxdnUHqV5PEnSRY,69
langextract-1.0.5.dist-info/licenses/LICENSE,sha256=Pd-b5cKP4n2tFDpdx27qJSIq0d1ok0oEcGTlbtL6QMU,11560
langextract-1.0.5.dist-info/top_level.txt,sha256=YhqMvlqMRu-LnMqFJ_QpmCHFkZPIauPkMlBREhDpw2g,12

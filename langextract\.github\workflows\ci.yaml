# Copyright 2025 Google LLC.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: CI

on:
  workflow_dispatch:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]
  pull_request_target:
    types: [labeled]

permissions:
  contents: read
  issues: write
  pull-requests: write

jobs:
  # Validates formatting on the PR branch directly (not the merge commit)
  # to ensure code style compliance before merging
  format-check:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - name: Checkout PR branch
        uses: actions/checkout@v4
        with:
          # Check the actual PR branch to catch formatting issues
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install format tools
        run: |
          python -m pip install --upgrade pip
          pip install tox

      - name: Check formatting
        id: format-check
        run: |
          tox -e format

      - name: Comment on PR if formatting fails
        if: always() && steps.format-check.outcome == 'failure'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.payload.pull_request.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `❌ **Formatting Check Failed**

            Your PR has formatting issues. Please run the following command locally and push the changes:

            \`\`\`bash
            ./autoformat.sh
            \`\`\`

            This will automatically fix all formatting issues using pyink (Google's Python formatter) and isort.`
            })

  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12"]
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -e ".[dev,test]"

      - name: Run unit tests and linting
        run: |
          PY_VERSION=$(echo "${{ matrix.python-version }}" | tr -d '.')
          # Format check is handled by separate job for better isolation
          tox -e py${PY_VERSION},lint-src,lint-tests

  live-api-tests:
    needs: test
    runs-on: ubuntu-latest
    if: |
      github.event_name == 'push' ||
      (github.event_name == 'pull_request' &&
       github.event.pull_request.head.repo.full_name == github.repository)

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -e ".[dev,test]"

      - name: Run live API tests
        env:
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          LANGEXTRACT_API_KEY: ${{ secrets.GEMINI_API_KEY }}  # For backward compatibility
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: |
          if [[ -z "$GEMINI_API_KEY" && -z "$OPENAI_API_KEY" ]]; then
            echo "::notice::Live API tests skipped - no provider secrets configured"
            exit 0
          fi
          tox -e live-api

  plugin-integration-test:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - uses: actions/checkout@v4

      - name: Detect provider-related changes
        id: provider-changes
        uses: tj-actions/changed-files@v46
        with:
          files: |
            langextract/providers/**
            langextract/factory.py
            langextract/inference.py
            tests/provider_plugin_test.py
            pyproject.toml
            .github/workflows/ci.yaml

      - name: Skip if no provider changes
        if: steps.provider-changes.outputs.any_changed == 'false'
        run: |
          echo "No provider-related changes detected – skipping plugin integration test."
          exit 0

      - name: Set up Python 3.11
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install tox

      - name: Run plugin smoke test
        run: tox -e plugin-smoke

      - name: Run plugin integration test
        run: tox -e plugin-integration

  ollama-integration-test:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - uses: actions/checkout@v4

      - name: Detect file changes
        id: changes
        uses: tj-actions/changed-files@v46
        with:
          files: |
            langextract/inference.py
            examples/ollama/**
            tests/test_ollama_integration.py
            .github/workflows/ci.yaml

      - name: Skip if no Ollama changes
        if: steps.changes.outputs.any_changed == 'false'
        run: |
          echo "No Ollama-related changes detected – skipping job."
          exit 0

      - name: Set up Python 3.11
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Launch Ollama container
        run: |
          docker run -d --name ollama \
            -p 127.0.0.1:11434:11434 \
            -v ollama:/root/.ollama \
            ollama/ollama:0.5.4
          for i in {1..20}; do
            curl -fs http://localhost:11434/api/version && break
            sleep 3
          done

      - name: Pull gemma2 model
        run: docker exec ollama ollama pull gemma2:2b || true

      - name: Install tox
        run: |
          python -m pip install --upgrade pip
          pip install tox

      - name: Run Ollama integration tests
        run: tox -e ollama-integration

  test-fork-pr:
    runs-on: ubuntu-latest
    # Triggered when a maintainer adds 'ready-to-merge' label
    if: |
      github.event_name == 'pull_request_target' &&
      github.event.action == 'labeled' &&
      contains(github.event.label.name, 'ready-to-merge')

    steps:
      - name: Check if user is maintainer
        uses: actions/github-script@v7
        with:
          script: |
            const { data: permission } = await github.rest.repos.getCollaboratorPermissionLevel({
              owner: context.repo.owner,
              repo: context.repo.repo,
              username: context.actor
            });

            const isMaintainer = ['admin', 'maintain'].includes(permission.permission);
            if (!isMaintainer) {
              throw new Error(`User ${context.actor} does not have maintainer permissions.`);
            }

      - name: Checkout PR branch directly
        uses: actions/checkout@v4
        with:
          # Validate formatting on actual PR code before running expensive tests
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0

      - name: Set up Python 3.11
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -e ".[dev,test]"

      - name: Validate PR formatting
        run: |
          echo "Validating code formatting..."
          tox -e format || {
            echo "::error::Code formatting does not meet project standards. Please run ./autoformat.sh locally and push the changes."
            exit 1
          }

      - name: Checkout main branch
        uses: actions/checkout@v4
        with:
          ref: main
          fetch-depth: 0

      - name: Merge PR safely for testing
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

          # pull_request_target runs in base repo context, so this is safe
          git fetch origin pull/${{ github.event.pull_request.number }}/head:pr-to-test
          git merge pr-to-test --no-ff --no-edit

      - name: Add status comment
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.payload.pull_request.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: 'Running live API tests... This will take a few minutes.'
            });

      - name: Run live API tests
        env:
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          LANGEXTRACT_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: |
          if [[ -z "$GEMINI_API_KEY" && -z "$OPENAI_API_KEY" ]]; then
            echo "::error::Live API tests skipped - no provider secrets configured"
            exit 1
          fi
          tox -e live-api

      - name: Report success
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.payload.pull_request.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '✅ Live API tests passed! All endpoints are working correctly.'
            });

      - name: Report failure
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.payload.pull_request.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '❌ Live API tests failed. Please check the workflow logs for details.'
            });

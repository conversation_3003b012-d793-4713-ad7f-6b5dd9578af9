# LangExtract 本地部署说明

## 概述
LangExtract 是 Google 开发的 Python 库，用于使用大语言模型从非结构化文本中提取结构化信息。

## 部署状态
✅ **已完成部署**
- 项目已克隆到本地
- 虚拟环境已创建并激活
- 所有依赖包已安装
- 演示脚本已准备就绪

## 目录结构
```
f:\ocr\langextract\
├── langextract_env\          # 虚拟环境
├── langextract\              # 核心库代码
├── examples\                 # 官方示例
├── tests\                    # 测试文件
├── .env                      # 环境变量配置文件
├── demo_gemini.py           # Gemini模型演示脚本
├── quickstart_gemini.py     # 快速开始脚本
└── 部署说明.md              # 本文档
```

## 使用步骤

### 1. 激活虚拟环境
```powershell
cd f:\ocr\langextract
.\langextract_env\Scripts\activate
```

### 2. 获取 Gemini API 密钥
1. 访问 [Google AI Studio](https://aistudio.google.com/)
2. 登录你的 Google 账户
3. 创建新的 API 密钥
4. 复制生成的 API 密钥

### 3. 配置 API 密钥

#### 方法一：使用 .env 文件（推荐）
编辑 `.env` 文件，将 `your-gemini-api-key-here` 替换为实际的 API 密钥：
```
LANGEXTRACT_API_KEY=你的实际API密钥
```

#### 方法二：直接在脚本中设置
编辑 `quickstart_gemini.py`，将 `API_KEY` 变量设置为你的实际密钥。

### 4. 运行演示

#### 快速开始（简单版本）
```powershell
python quickstart_gemini.py
```

#### 完整演示（包含可视化）
```powershell
python demo_gemini.py
```

#### 多语言演示（支持中文和日语，修复编码问题）
```powershell
python demo_multilang.py
```

#### 单独测试日语
```powershell
python test_japanese.py
```

## 功能特性

### 核心功能
- **精确源定位**: 将每个提取映射到源文本中的确切位置
- **可靠的结构化输出**: 基于少样本示例强制执行一致的输出模式
- **长文档优化**: 通过文本分块、并行处理和多轮处理优化大文档提取
- **交互式可视化**: 生成独立的交互式HTML文件来可视化提取结果
- **灵活的LLM支持**: 支持云端和本地模型

### 支持的模型
- **Gemini 2.5 Flash** (推荐): 速度、成本和质量的最佳平衡
- **Gemini 2.5 Pro**: 适用于需要深度推理的复杂任务
- **OpenAI GPT-4o**: 需要额外安装 `pip install langextract[openai]`
- **本地模型**: 通过 Ollama 支持本地推理

## 示例用法

### 基本文本提取
```python
import langextract as lx

# 定义提取任务
prompt = "从文本中提取人物和情感"

# 提供示例
examples = [
    lx.data.ExampleData(
        text="小明很开心",
        extractions=[
            lx.data.Extraction(
                extraction_class="人物",
                extraction_text="小明",
                attributes={"情绪": "开心"}
            )
        ]
    )
]

# 执行提取
result = lx.extract(
    text_or_documents="李华在笑",
    prompt_description=prompt,
    examples=examples,
    model_id="gemini-2.5-flash"
)
```

### 长文档处理
```python
# 处理大文档（如整本小说）
result = lx.extract(
    text_or_documents="https://www.gutenberg.org/files/1513/1513-0.txt",
    prompt_description=prompt,
    examples=examples,
    model_id="gemini-2.5-flash",
    extraction_passes=3,    # 多轮提取提高召回率
    max_workers=20,         # 并行处理加速
    max_char_buffer=1000    # 更小的上下文提高准确性
)
```

## 故障排除

### 常见问题

1. **API密钥错误**
   - 确保API密钥正确设置
   - 检查API密钥是否有效且未过期

2. **网络连接问题**
   - 确保网络连接正常
   - 检查防火墙设置

3. **依赖包问题**
   - 确保虚拟环境已激活
   - 重新安装依赖：`pip install -e .`

4. **模型访问限制**
   - 检查Gemini API的使用配额
   - 考虑升级到Tier 2配额以提高吞吐量

### 性能优化建议

1. **对于大规模使用**：
   - 使用 Tier 2 Gemini 配额
   - 调整 `max_workers` 参数
   - 使用 `extraction_passes` 提高召回率

2. **对于复杂任务**：
   - 使用 `gemini-2.5-pro` 模型
   - 提供更详细的示例
   - 调整 `max_char_buffer` 参数

## 相关链接

- [项目主页](https://github.com/google/langextract)
- [Google AI Studio](https://aistudio.google.com/)
- [Gemini API 文档](https://ai.google.dev/docs)
- [使用条款](https://ai.google.dev/gemma/terms)

## 许可证
本项目使用 Apache 2.0 许可证。这不是 Google 官方支持的产品。

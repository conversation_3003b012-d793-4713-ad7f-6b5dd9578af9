#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangExtract Gemini デモスクリプト
Gemini モデルを使用してテキストから構造化情報を抽出するデモ
支持中文和日语的编码修复版本
"""

import os
import sys
import json
import langextract as lx
import textwrap
from dotenv import load_dotenv

# Windows环境下设置控制台编码为UTF-8
if sys.platform.startswith('win'):
    try:
        # 设置控制台代码页为UTF-8
        os.system('chcp 65001 >nul')
        # 重新配置标准输出编码
        import codecs
        sys.stdout.reconfigure(encoding='utf-8')
        sys.stderr.reconfigure(encoding='utf-8')
    except:
        pass

def main():
    """メイン関数"""
    print("=== LangExtract Gemini デモ ===")
    
    # .envファイルから環境変数を読み込み
    load_dotenv()
    
    # APIキーの確認
    api_key = os.getenv('LANGEXTRACT_API_KEY')
    if not api_key or api_key == 'your-gemini-api-key-here':
        print("❌ エラー: LANGEXTRACT_API_KEY が設定されていません")
        print("📝 手順:")
        print("1. Google AI Studio (https://aistudio.google.com/) でAPIキーを取得")
        print("2. .envファイルのLANGEXTRACT_API_KEYを実際のAPIキーに置き換え")
        print("3. このスクリプトを再実行")
        return
    
    print(f"✅ APIキーが設定されています (先頭4文字: {api_key[:4]}...)")
    
    # 1. 抽取任务定义
    prompt = textwrap.dedent("""\
        从文本中提取人物、情感和关系，按出现顺序排列。
        使用准确的文本进行提取，不要改写或重叠实体。
        为每个实体提供有意义的属性以添加上下文。""")

    # 2. 提供高质量示例来指导模型
    examples = [
        lx.data.ExampleData(
            text="小明高兴地对小红说：'我们是最好的朋友！'",
            extractions=[
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="小明",
                    attributes={"情绪状态": "高兴", "角色": "说话者"}
                ),
                lx.data.Extraction(
                    extraction_class="情感",
                    extraction_text="高兴地",
                    attributes={"情感类型": "喜悦", "表达方式": "副词"}
                ),
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="小红",
                    attributes={"角色": "听话者"}
                ),
                lx.data.Extraction(
                    extraction_class="关系",
                    extraction_text="我们是最好的朋友",
                    attributes={"关系类型": "友谊", "强度": "最好"}
                ),
            ]
        )
    ]

    # 3. 要处理的文本
    input_text = "李华在图书馆里专心地阅读，他对知识的渴望让周围的同学都很敬佩。"
    
    print(f"📄 処理するテキスト: {input_text}")
    print("🔄 抽出を実行中...")
    
    try:
        # 4. 抽出の実行
        result = lx.extract(
            text_or_documents=input_text,
            prompt_description=prompt,
            examples=examples,
            model_id="gemini-2.5-flash",  # 推奨デフォルトモデル
            api_key=api_key
        )
        
        print("✅ 提取完成！")
        print("\n📊 提取结果:")
        print("-" * 50)

        # 结果显示
        for i, extraction in enumerate(result.extractions, 1):
            print(f"{i}. 类别: {extraction.extraction_class}")
            print(f"   文本: '{extraction.extraction_text}'")
            print(f"   属性: {extraction.attributes}")
            print()

        # 5. 将结果保存到JSONL文件（确保UTF-8编码）
        output_file = "extraction_results.jsonl"

        # 手动保存以确保编码正确
        try:
            with open(output_file, "w", encoding="utf-8", newline='') as f:
                # 创建文档数据
                doc_data = {
                    "text": result.text,
                    "document_id": result.document_id,
                    "extractions": []
                }

                # 添加提取结果
                for extraction in result.extractions:
                    extraction_data = {
                        "extraction_class": extraction.extraction_class,
                        "extraction_text": extraction.extraction_text,
                        "attributes": extraction.attributes,
                        "char_interval": extraction.char_interval,
                        "alignment_status": extraction.alignment_status,
                        "extraction_index": extraction.extraction_index,
                        "group_index": extraction.group_index,
                        "description": extraction.description
                    }
                    doc_data["extractions"].append(extraction_data)

                # 写入JSON行（确保中文和日文正确显示）
                json.dump(doc_data, f, ensure_ascii=False, indent=None, separators=(',', ':'))
                f.write('\n')

            print(f"💾 结果已保存到 {output_file}")

            # 验证保存的文件
            print("🔍 验证保存的文件内容:")
            with open(output_file, "r", encoding="utf-8") as f:
                content = f.read()
                print(f"文件大小: {len(content)} 字符")

                # 解析并显示内容
                try:
                    data = json.loads(content.strip())
                    print("✅ JSON格式正确")
                    print(f"提取数量: {len(data.get('extractions', []))}")
                except json.JSONDecodeError as je:
                    print(f"⚠️ JSON解析错误: {je}")

        except Exception as save_error:
            print(f"⚠️ 手动保存失败，尝试使用默认方法: {save_error}")
            # 备用保存方法
            lx.io.save_annotated_documents([result], output_name=output_file, output_dir=".")
            print(f"💾 结果已保存到 {output_file} (使用默认方法)")

        # 6. 生成交互式可视化HTML
        print("🎨 正在生成可视化HTML...")
        try:
            html_content = lx.visualize(output_file)
            html_file = "visualization.html"
            with open(html_file, "w", encoding="utf-8", newline='') as f:
                f.write(html_content)
            print(f"🌐 可视化文件 {html_file} 已生成")
            print(f"   可以在浏览器中打开 {html_file} 查看结果")
        except Exception as viz_error:
            print(f"⚠️ 可视化生成失败: {viz_error}")
            print("但提取结果已成功保存到JSONL文件")
        
    except Exception as e:
        print(f"❌ エラーが発生しました: {e}")
        print("💡 トラブルシューティング:")
        print("- APIキーが正しく設定されているか確認")
        print("- インターネット接続を確認")
        print("- Gemini APIの利用制限を確認")

if __name__ == "__main__":
    main()

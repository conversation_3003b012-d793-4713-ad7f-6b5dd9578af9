#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangExtract Gemini デモスクリプト
Gemini モデルを使用してテキストから構造化情報を抽出するデモ
"""

import os
import langextract as lx
import textwrap
from dotenv import load_dotenv

def main():
    """メイン関数"""
    print("=== LangExtract Gemini デモ ===")
    
    # .envファイルから環境変数を読み込み
    load_dotenv()
    
    # APIキーの確認
    api_key = os.getenv('LANGEXTRACT_API_KEY')
    if not api_key or api_key == 'your-gemini-api-key-here':
        print("❌ エラー: LANGEXTRACT_API_KEY が設定されていません")
        print("📝 手順:")
        print("1. Google AI Studio (https://aistudio.google.com/) でAPIキーを取得")
        print("2. .envファイルのLANGEXTRACT_API_KEYを実際のAPIキーに置き換え")
        print("3. このスクリプトを再実行")
        return
    
    print(f"✅ APIキーが設定されています (先頭4文字: {api_key[:4]}...)")
    
    # 1. 抽取任务定义
    prompt = textwrap.dedent("""\
        从文本中提取人物、情感和关系，按出现顺序排列。
        使用准确的文本进行提取，不要改写或重叠实体。
        为每个实体提供有意义的属性以添加上下文。""")

    # 2. 提供高质量示例来指导模型
    examples = [
        lx.data.ExampleData(
            text="小明高兴地对小红说：'我们是最好的朋友！'",
            extractions=[
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="小明",
                    attributes={"情绪状态": "高兴", "角色": "说话者"}
                ),
                lx.data.Extraction(
                    extraction_class="情感",
                    extraction_text="高兴地",
                    attributes={"情感类型": "喜悦", "表达方式": "副词"}
                ),
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="小红",
                    attributes={"角色": "听话者"}
                ),
                lx.data.Extraction(
                    extraction_class="关系",
                    extraction_text="我们是最好的朋友",
                    attributes={"关系类型": "友谊", "强度": "最好"}
                ),
            ]
        )
    ]

    # 3. 要处理的文本
    input_text = "李华在图书馆里专心地阅读，他对知识的渴望让周围的同学都很敬佩。"
    
    print(f"📄 処理するテキスト: {input_text}")
    print("🔄 抽出を実行中...")
    
    try:
        # 4. 抽出の実行
        result = lx.extract(
            text_or_documents=input_text,
            prompt_description=prompt,
            examples=examples,
            model_id="gemini-2.5-flash",  # 推奨デフォルトモデル
            api_key=api_key
        )
        
        print("✅ 抽出が完了しました！")
        print("\n📊 抽出結果:")
        print("-" * 50)
        
        # 結果の表示
        for i, extraction in enumerate(result.extractions, 1):
            print(f"{i}. クラス: {extraction.extraction_class}")
            print(f"   テキスト: '{extraction.extraction_text}'")
            print(f"   属性: {extraction.attributes}")
            print()
        
        # 5. 結果をJSONLファイルに保存
        output_file = "extraction_results.jsonl"
        lx.io.save_annotated_documents([result], output_name=output_file, output_dir=".")
        print(f"💾 結果を {output_file} に保存しました")
        
        # 6. インタラクティブな可視化HTMLを生成
        print("🎨 可視化HTMLを生成中...")
        html_content = lx.visualize(output_file)
        html_file = "visualization.html"
        with open(html_file, "w", encoding="utf-8") as f:
            f.write(html_content)
        print(f"🌐 可視化ファイル {html_file} を生成しました")
        print(f"   ブラウザで {html_file} を開いて結果を確認できます")
        
    except Exception as e:
        print(f"❌ エラーが発生しました: {e}")
        print("💡 トラブルシューティング:")
        print("- APIキーが正しく設定されているか確認")
        print("- インターネット接続を確認")
        print("- Gemini APIの利用制限を確認")

if __name__ == "__main__":
    main()

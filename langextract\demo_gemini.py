#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangExtract Gemini デモスクリプト
Gemini モデルを使用してテキストから構造化情報を抽出するデモ
"""

import os
import langextract as lx
import textwrap
from dotenv import load_dotenv

def main():
    """メイン関数"""
    print("=== LangExtract Gemini デモ ===")
    
    # .envファイルから環境変数を読み込み
    load_dotenv()
    
    # APIキーの確認
    api_key = os.getenv('LANGEXTRACT_API_KEY')
    if not api_key or api_key == 'your-gemini-api-key-here':
        print("❌ エラー: LANGEXTRACT_API_KEY が設定されていません")
        print("📝 手順:")
        print("1. Google AI Studio (https://aistudio.google.com/) でAPIキーを取得")
        print("2. .envファイルのLANGEXTRACT_API_KEYを実際のAPIキーに置き換え")
        print("3. このスクリプトを再実行")
        return
    
    print(f"✅ APIキーが設定されています (先頭4文字: {api_key[:4]}...)")
    
    # 1. 抽出タスクの定義
    prompt = textwrap.dedent("""\
        テキストから人物、感情、関係性を出現順に抽出してください。
        抽出には正確なテキストを使用し、言い換えや重複は避けてください。
        各エンティティに意味のある属性を付けてコンテキストを追加してください。""")
    
    # 2. 高品質な例を提供してモデルをガイド
    examples = [
        lx.data.ExampleData(
            text="太郎は悲しそうに花子を見つめていた。彼女への愛は深かった。",
            extractions=[
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="太郎",
                    attributes={"感情状態": "悲しみ", "役割": "主人公"}
                ),
                lx.data.Extraction(
                    extraction_class="感情",
                    extraction_text="悲しそうに",
                    attributes={"感情の種類": "悲しみ", "表現方法": "表情"}
                ),
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="花子",
                    attributes={"役割": "愛の対象"}
                ),
                lx.data.Extraction(
                    extraction_class="関係性",
                    extraction_text="彼女への愛は深かった",
                    attributes={"関係の種類": "恋愛", "強度": "深い"}
                ),
            ]
        )
    ]
    
    # 3. 処理するテキスト
    input_text = "美咲は窓辺で星空を眺めながら、遠く離れた恋人の健太のことを想っていた。二人の絆は時間と距離を超えて続いている。"
    
    print(f"📄 処理するテキスト: {input_text}")
    print("🔄 抽出を実行中...")
    
    try:
        # 4. 抽出の実行
        result = lx.extract(
            text_or_documents=input_text,
            prompt_description=prompt,
            examples=examples,
            model_id="gemini-2.5-flash",  # 推奨デフォルトモデル
            api_key=api_key
        )
        
        print("✅ 抽出が完了しました！")
        print("\n📊 抽出結果:")
        print("-" * 50)
        
        # 結果の表示
        for i, extraction in enumerate(result.extractions, 1):
            print(f"{i}. クラス: {extraction.extraction_class}")
            print(f"   テキスト: '{extraction.extraction_text}'")
            print(f"   属性: {extraction.attributes}")
            print()
        
        # 5. 結果をJSONLファイルに保存
        output_file = "extraction_results.jsonl"
        lx.io.save_annotated_documents([result], output_name=output_file, output_dir=".")
        print(f"💾 結果を {output_file} に保存しました")
        
        # 6. インタラクティブな可視化HTMLを生成
        print("🎨 可視化HTMLを生成中...")
        html_content = lx.visualize(output_file)
        html_file = "visualization.html"
        with open(html_file, "w", encoding="utf-8") as f:
            f.write(html_content)
        print(f"🌐 可視化ファイル {html_file} を生成しました")
        print(f"   ブラウザで {html_file} を開いて結果を確認できます")
        
    except Exception as e:
        print(f"❌ エラーが発生しました: {e}")
        print("💡 トラブルシューティング:")
        print("- APIキーが正しく設定されているか確認")
        print("- インターネット接続を確認")
        print("- Gemini APIの利用制限を確認")

if __name__ == "__main__":
    main()

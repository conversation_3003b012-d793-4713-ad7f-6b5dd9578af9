#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangExtract 快速开始脚本 - Gemini版本
最简单的使用示例
"""

import langextract as lx
import textwrap

# 设置你的API密钥（请替换为实际的API密钥）
API_KEY = "your-gemini-api-key-here"

def main():
    print("🚀 LangExtract 快速开始 - Gemini模型")
    
    # 1. 定义提取任务
    prompt = textwrap.dedent("""\
        从文本中提取人物、情感和关系，按出现顺序排列。
        使用准确的文本进行提取，不要改写或重叠实体。
        为每个实体提供有意义的属性以添加上下文。""")
    
    # 2. 提供示例来指导模型
    examples = [
        lx.data.ExampleData(
            text="小明高兴地对小红说：'我们是最好的朋友！'",
            extractions=[
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="小明",
                    attributes={"情绪状态": "高兴"}
                ),
                lx.data.Extraction(
                    extraction_class="情感",
                    extraction_text="高兴地",
                    attributes={"情感类型": "喜悦"}
                ),
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="小红",
                    attributes={"角色": "对话对象"}
                ),
                lx.data.Extraction(
                    extraction_class="关系",
                    extraction_text="我们是最好的朋友",
                    attributes={"关系类型": "友谊"}
                ),
            ]
        )
    ]
    
    # 3. 要处理的文本
    input_text = "李华在图书馆里专心地阅读，他对知识的渴望让周围的同学都很敬佩。"
    
    print(f"📝 输入文本: {input_text}")
    
    # 检查API密钥
    if API_KEY == "your-gemini-api-key-here":
        print("⚠️  请先设置你的Gemini API密钥！")
        print("📋 获取API密钥的步骤:")
        print("1. 访问 https://aistudio.google.com/")
        print("2. 创建API密钥")
        print("3. 将API_KEY变量替换为你的实际密钥")
        return
    
    try:
        print("🔄 正在处理...")
        
        # 4. 执行提取
        result = lx.extract(
            text_or_documents=input_text,
            prompt_description=prompt,
            examples=examples,
            model_id="gemini-2.5-flash",
            api_key=API_KEY
        )
        
        print("✅ 提取完成！")
        print("\n📊 结果:")
        print("-" * 40)
        
        for i, extraction in enumerate(result.extractions, 1):
            print(f"{i}. 类别: {extraction.extraction_class}")
            print(f"   文本: '{extraction.extraction_text}'")
            print(f"   属性: {extraction.attributes}")
            print()
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("💡 请检查:")
        print("- API密钥是否正确")
        print("- 网络连接是否正常")

if __name__ == "__main__":
    main()

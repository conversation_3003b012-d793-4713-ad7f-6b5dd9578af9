#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangExtract 多语言演示脚本
支持中文和日语的编码修复版本
Multilingual demo script supporting Chinese and Japanese
"""

import os
import sys
import json
import langextract as lx
import textwrap
from dotenv import load_dotenv

# Windows环境下设置控制台编码为UTF-8
if sys.platform.startswith('win'):
    try:
        # 设置控制台代码页为UTF-8
        os.system('chcp 65001 >nul')
        # 重新配置标准输出编码
        sys.stdout.reconfigure(encoding='utf-8')
        sys.stderr.reconfigure(encoding='utf-8')
    except Exception as e:
        print(f"编码设置警告: {e}")

def save_results_with_encoding(result, filename):
    """
    保存结果到文件，确保正确的UTF-8编码
    """
    try:
        with open(filename, "w", encoding="utf-8", newline='') as f:
            # 创建文档数据
            doc_data = {
                "text": result.text,
                "document_id": result.document_id,
                "extractions": []
            }
            
            # 添加提取结果
            for extraction in result.extractions:
                extraction_data = {
                    "extraction_class": extraction.extraction_class,
                    "extraction_text": extraction.extraction_text,
                    "attributes": extraction.attributes,
                    "char_interval": extraction.char_interval,
                    "alignment_status": extraction.alignment_status,
                    "extraction_index": extraction.extraction_index,
                    "group_index": extraction.group_index,
                    "description": extraction.description
                }
                doc_data["extractions"].append(extraction_data)
            
            # 写入JSON行（确保中文和日文正确显示）
            json.dump(doc_data, f, ensure_ascii=False, indent=2, separators=(',', ': '))
            f.write('\n')
        
        return True
    except Exception as e:
        print(f"保存失败: {e}")
        return False

def test_chinese():
    """测试中文提取"""
    print("\n" + "="*50)
    print("🇨🇳 中文测试")
    print("="*50)
    
    # 中文提取任务
    prompt_cn = textwrap.dedent("""\
        从文本中提取人物、情感和关系，按出现顺序排列。
        使用准确的文本进行提取，不要改写或重叠实体。
        为每个实体提供有意义的属性以添加上下文。""")
    
    # 中文示例
    examples_cn = [
        lx.data.ExampleData(
            text="小明高兴地对小红说：'我们是最好的朋友！'",
            extractions=[
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="小明",
                    attributes={"情绪状态": "高兴", "角色": "说话者"}
                ),
                lx.data.Extraction(
                    extraction_class="情感",
                    extraction_text="高兴地",
                    attributes={"情感类型": "喜悦", "表达方式": "副词"}
                ),
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="小红",
                    attributes={"角色": "听话者"}
                ),
                lx.data.Extraction(
                    extraction_class="关系",
                    extraction_text="我们是最好的朋友",
                    attributes={"关系类型": "友谊", "强度": "最好"}
                ),
            ]
        )
    ]
    
    # 中文测试文本
    input_text_cn = "李华在图书馆里专心地阅读，他对知识的渴望让周围的同学都很敬佩。"
    
    return prompt_cn, examples_cn, input_text_cn

def test_japanese():
    """测试日语提取"""
    print("\n" + "="*50)
    print("🇯🇵 日本語テスト")
    print("="*50)
    
    # 日语提取任务
    prompt_jp = textwrap.dedent("""\
        テキストから人物、感情、関係性を出現順に抽出してください。
        抽出には正確なテキストを使用し、言い換えや重複は避けてください。
        各エンティティに意味のある属性を付けてコンテキストを追加してください。""")
    
    # 日语示例
    examples_jp = [
        lx.data.ExampleData(
            text="太郎は悲しそうに花子を見つめていた。彼女への愛は深かった。",
            extractions=[
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="太郎",
                    attributes={"感情状態": "悲しみ", "役割": "主人公"}
                ),
                lx.data.Extraction(
                    extraction_class="感情",
                    extraction_text="悲しそうに",
                    attributes={"感情の種類": "悲しみ", "表現方法": "表情"}
                ),
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="花子",
                    attributes={"役割": "愛の対象"}
                ),
                lx.data.Extraction(
                    extraction_class="関係性",
                    extraction_text="彼女への愛は深かった",
                    attributes={"関係の種類": "恋愛", "強度": "深い"}
                ),
            ]
        )
    ]
    
    # 日语测试文本
    input_text_jp = "美咲は窓辺で星空を眺めながら、遠く離れた恋人の健太のことを想っていた。二人の絆は時間と距離を超えて続いている。"
    
    return prompt_jp, examples_jp, input_text_jp

def main():
    """主函数"""
    print("🌍 LangExtract 多语言演示")
    print("支持中文和日语的编码修复版本")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv('LANGEXTRACT_API_KEY')
    if not api_key or api_key == 'your-gemini-api-key-here':
        print("❌ 错误: LANGEXTRACT_API_KEY 未设置")
        print("📝 步骤:")
        print("1. 访问 Google AI Studio (https://aistudio.google.com/) 获取API密钥")
        print("2. 在.env文件中设置LANGEXTRACT_API_KEY为实际的API密钥")
        print("3. 重新运行此脚本")
        return
    
    print(f"✅ API密钥已设置 (前4位: {api_key[:4]}...)")
    
    # 选择测试语言
    print("\n请选择测试语言:")
    print("1. 中文测试")
    print("2. 日语测试")
    print("3. 两种语言都测试")
    
    try:
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == "1":
            # 中文测试
            prompt, examples, input_text = test_chinese()
            run_extraction(prompt, examples, input_text, api_key, "chinese")
            
        elif choice == "2":
            # 日语测试
            prompt, examples, input_text = test_japanese()
            run_extraction(prompt, examples, input_text, api_key, "japanese")
            
        elif choice == "3":
            # 两种语言都测试
            print("🔄 开始中文测试...")
            prompt_cn, examples_cn, input_text_cn = test_chinese()
            run_extraction(prompt_cn, examples_cn, input_text_cn, api_key, "chinese")
            
            print("\n🔄 开始日语测试...")
            prompt_jp, examples_jp, input_text_jp = test_japanese()
            run_extraction(prompt_jp, examples_jp, input_text_jp, api_key, "japanese")
            
        else:
            print("⚠️ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

def run_extraction(prompt, examples, input_text, api_key, language):
    """运行提取任务"""
    print(f"📄 输入文本: {input_text}")
    print("🔄 正在执行提取...")
    
    try:
        # 执行提取
        result = lx.extract(
            text_or_documents=input_text,
            prompt_description=prompt,
            examples=examples,
            model_id="gemini-2.5-flash",
            api_key=api_key
        )
        
        print("✅ 提取完成！")
        print("\n📊 提取结果:")
        print("-" * 50)
        
        # 显示结果
        for i, extraction in enumerate(result.extractions, 1):
            print(f"{i}. 类别: {extraction.extraction_class}")
            print(f"   文本: '{extraction.extraction_text}'")
            print(f"   属性: {extraction.attributes}")
            print()
        
        # 保存结果
        output_file = f"extraction_results_{language}.jsonl"
        if save_results_with_encoding(result, output_file):
            print(f"💾 结果已保存到 {output_file}")
            
            # 验证文件内容
            with open(output_file, "r", encoding="utf-8") as f:
                content = f.read()
                try:
                    data = json.loads(content.strip())
                    print("✅ JSON格式正确，编码无问题")
                    print(f"提取数量: {len(data.get('extractions', []))}")
                except json.JSONDecodeError:
                    print("⚠️ JSON格式有问题")
        else:
            print("❌ 保存失败")
        
    except Exception as e:
        print(f"❌ 提取失败: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日语测试脚本
"""

import os
import sys
import json
import langextract as lx
import textwrap
from dotenv import load_dotenv

# Windows环境下设置控制台编码为UTF-8
if sys.platform.startswith('win'):
    try:
        os.system('chcp 65001 >nul')
        sys.stdout.reconfigure(encoding='utf-8')
        sys.stderr.reconfigure(encoding='utf-8')
    except:
        pass

def save_results_with_encoding(result, filename):
    """保存结果到文件，确保正确的UTF-8编码"""
    try:
        with open(filename, "w", encoding="utf-8", newline='') as f:
            doc_data = {
                "text": result.text,
                "document_id": result.document_id,
                "extractions": []
            }
            
            for extraction in result.extractions:
                extraction_data = {
                    "extraction_class": extraction.extraction_class,
                    "extraction_text": extraction.extraction_text,
                    "attributes": extraction.attributes,
                    "char_interval": extraction.char_interval,
                    "alignment_status": extraction.alignment_status,
                    "extraction_index": extraction.extraction_index,
                    "group_index": extraction.group_index,
                    "description": extraction.description
                }
                doc_data["extractions"].append(extraction_data)
            
            json.dump(doc_data, f, ensure_ascii=False, indent=2, separators=(',', ': '))
            f.write('\n')
        
        return True
    except Exception as e:
        print(f"保存失败: {e}")
        return False

def main():
    """主函数"""
    print("🇯🇵 日本語テスト")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv('LANGEXTRACT_API_KEY')
    if not api_key:
        print("❌ API密钥未设置")
        return
    
    print(f"✅ API密钥已设置")
    
    # 日语提取任务
    prompt = textwrap.dedent("""\
        テキストから人物、感情、関係性を出現順に抽出してください。
        抽出には正確なテキストを使用し、言い換えや重複は避けてください。
        各エンティティに意味のある属性を付けてコンテキストを追加してください。""")
    
    # 日语示例
    examples = [
        lx.data.ExampleData(
            text="太郎は悲しそうに花子を見つめていた。彼女への愛は深かった。",
            extractions=[
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="太郎",
                    attributes={"感情状態": "悲しみ", "役割": "主人公"}
                ),
                lx.data.Extraction(
                    extraction_class="感情",
                    extraction_text="悲しそうに",
                    attributes={"感情の種類": "悲しみ", "表現方法": "表情"}
                ),
                lx.data.Extraction(
                    extraction_class="人物",
                    extraction_text="花子",
                    attributes={"役割": "愛の対象"}
                ),
                lx.data.Extraction(
                    extraction_class="関係性",
                    extraction_text="彼女への愛は深かった",
                    attributes={"関係の種類": "恋愛", "強度": "深い"}
                ),
            ]
        )
    ]
    
    # 日语测试文本
    input_text = "美咲は窓辺で星空を眺めながら、遠く離れた恋人の健太のことを想っていた。二人の絆は時間と距離を超えて続いている。"
    
    print(f"📄 入力テキスト: {input_text}")
    print("🔄 抽出を実行中...")
    
    try:
        # 执行提取
        result = lx.extract(
            text_or_documents=input_text,
            prompt_description=prompt,
            examples=examples,
            model_id="gemini-2.5-flash",
            api_key=api_key
        )
        
        print("✅ 抽出完了！")
        print("\n📊 抽出結果:")
        print("-" * 50)
        
        # 显示结果
        for i, extraction in enumerate(result.extractions, 1):
            print(f"{i}. クラス: {extraction.extraction_class}")
            print(f"   テキスト: '{extraction.extraction_text}'")
            print(f"   属性: {extraction.attributes}")
            print()
        
        # 保存结果
        output_file = "extraction_results_japanese.jsonl"
        if save_results_with_encoding(result, output_file):
            print(f"💾 結果を {output_file} に保存しました")
            
            # 验证文件内容
            with open(output_file, "r", encoding="utf-8") as f:
                content = f.read()
                try:
                    data = json.loads(content.strip())
                    print("✅ JSON形式正しい、エンコーディング問題なし")
                    print(f"抽出数: {len(data.get('extractions', []))}")
                except json.JSONDecodeError:
                    print("⚠️ JSON形式に問題があります")
        else:
            print("❌ 保存失敗")
        
    except Exception as e:
        print(f"❌ 抽出失敗: {e}")

if __name__ == "__main__":
    main()
